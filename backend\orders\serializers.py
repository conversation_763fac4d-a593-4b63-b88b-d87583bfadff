"""
Serializers for the orders app.
"""

from rest_framework import serializers
from django.db import transaction
from .models import Product, Order, OrderItem, DailyMenu, DailyMenuItem
from accounts.models import Business


class ProductSerializer(serializers.ModelSerializer):
    """
    Serializer for products.
    """
    business_name = serializers.CharField(source='business.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Product
        fields = (
            'id', 'business', 'business_name', 'name', 'description',
            'price', 'sku', 'category', 'image', 'stock_quantity',
            'low_stock_threshold', 'status', 'status_display',
            'is_in_stock', 'is_low_stock', 'created_at', 'updated_at'
        )
        read_only_fields = (
            'id', 'business', 'is_in_stock', 'is_low_stock',
            'created_at', 'updated_at'
        )


class ProductCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating products.
    """
    
    class Meta:
        model = Product
        fields = (
            'name', 'description', 'price', 'sku', 'category',
            'image', 'stock_quantity', 'low_stock_threshold', 'status'
        )
    
    def create(self, validated_data):
        # Set the business from the request context
        business = self.context['request'].user.business
        validated_data['business'] = business
        return super().create(validated_data)


class OrderItemSerializer(serializers.ModelSerializer):
    """
    Serializer for order items.
    """
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_price = serializers.DecimalField(
        source='product.price', 
        max_digits=10, 
        decimal_places=2, 
        read_only=True
    )
    
    class Meta:
        model = OrderItem
        fields = (
            'id', 'product', 'product_name', 'product_price',
            'quantity', 'unit_price', 'total_price', 'notes'
        )
        read_only_fields = ('id', 'unit_price', 'total_price')


class OrderItemCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating order items.
    """
    
    class Meta:
        model = OrderItem
        fields = ('product', 'quantity', 'notes')
    
    def validate_quantity(self, value):
        if value <= 0:
            raise serializers.ValidationError("Quantity must be greater than 0")
        return value
    
    def validate_product(self, value):
        if value.status != 'active':
            raise serializers.ValidationError("Product is not available")
        if not value.is_in_stock:
            raise serializers.ValidationError("Product is out of stock")
        return value


class OrderSerializer(serializers.ModelSerializer):
    """
    Serializer for orders.
    """
    items = OrderItemSerializer(many=True, read_only=True)
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    customer_phone = serializers.CharField(source='customer.phone_number', read_only=True)
    business_name = serializers.CharField(source='business.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Order
        fields = (
            'id', 'order_number', 'customer', 'customer_name',
            'customer_phone', 'business', 'business_name', 'status',
            'status_display', 'subtotal', 'delivery_fee', 'total_amount',
            'delivery_address', 'delivery_phone', 'delivery_notes',
            'items', 'created_at', 'updated_at', 'confirmed_at',
            'delivered_at', 'whatsapp_message_id'
        )
        read_only_fields = (
            'id', 'order_number', 'customer', 'business', 'subtotal',
            'total_amount', 'created_at', 'updated_at', 'confirmed_at',
            'delivered_at'
        )


class OrderCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating orders.
    """
    items = OrderItemCreateSerializer(many=True)
    
    class Meta:
        model = Order
        fields = (
            'business', 'delivery_address', 'delivery_phone',
            'delivery_notes', 'delivery_fee', 'items'
        )
    
    def validate_items(self, value):
        if not value:
            raise serializers.ValidationError("At least one item is required")
        return value
    
    def validate_business(self, value):
        if value.status != 'active':
            raise serializers.ValidationError("Business is not active")
        return value
    
    @transaction.atomic
    def create(self, validated_data):
        items_data = validated_data.pop('items')
        
        # Set customer from request
        validated_data['customer'] = self.context['request'].user
        
        # Create order
        order = Order.objects.create(**validated_data)
        
        # Create order items
        for item_data in items_data:
            OrderItem.objects.create(order=order, **item_data)
        
        # Recalculate totals
        order.calculate_totals()
        order.save()
        
        return order


class OrderUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating order status and details.
    """

    class Meta:
        model = Order
        fields = ('status', 'delivery_notes', 'delivery_fee')


class DailyMenuItemSerializer(serializers.ModelSerializer):
    """
    Serializer for DailyMenuItem model.
    """
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_description = serializers.CharField(source='product.description', read_only=True)
    product_image = serializers.ImageField(source='product.image', read_only=True)
    effective_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    is_sold_out = serializers.BooleanField(read_only=True)
    is_orderable = serializers.BooleanField(read_only=True)

    class Meta:
        model = DailyMenuItem
        fields = (
            'id', 'product', 'product_name', 'product_description', 'product_image',
            'available_quantity', 'remaining_quantity', 'is_available',
            'special_price', 'effective_price', 'notes',
            'is_sold_out', 'is_orderable', 'created_at', 'updated_at'
        )
        read_only_fields = (
            'id', 'product_name', 'product_description', 'product_image',
            'effective_price', 'is_sold_out', 'is_orderable',
            'created_at', 'updated_at'
        )


class DailyMenuSerializer(serializers.ModelSerializer):
    """
    Serializer for DailyMenu model.
    """
    business_name = serializers.CharField(source='business.name', read_only=True)
    menu_items = DailyMenuItemSerializer(many=True, read_only=True)
    effective_preparation_time = serializers.IntegerField(read_only=True)

    class Meta:
        model = DailyMenu
        fields = (
            'id', 'business', 'business_name', 'date', 'is_active',
            'preparation_time_minutes', 'effective_preparation_time',
            'notes', 'menu_items', 'created_at', 'updated_at'
        )
        read_only_fields = (
            'id', 'business_name', 'menu_items', 'effective_preparation_time',
            'created_at', 'updated_at'
        )

    def create(self, validated_data):
        # Set the business from the request context
        request = self.context.get('request')
        if request and hasattr(request.user, 'business'):
            validated_data['business'] = request.user.business
        return super().create(validated_data)


class DailyMenuCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating daily menu with items.
    """
    menu_items = DailyMenuItemSerializer(many=True, write_only=True)

    class Meta:
        model = DailyMenu
        fields = (
            'date', 'is_active', 'preparation_time_minutes', 'notes', 'menu_items'
        )

    @transaction.atomic
    def create(self, validated_data):
        menu_items_data = validated_data.pop('menu_items', [])

        # Set the business from the request context
        request = self.context.get('request')
        if request and hasattr(request.user, 'business'):
            validated_data['business'] = request.user.business

        # Create daily menu
        daily_menu = DailyMenu.objects.create(**validated_data)

        # Create menu items
        for item_data in menu_items_data:
            DailyMenuItem.objects.create(daily_menu=daily_menu, **item_data)

        return daily_menu
    
    def validate_status(self, value):
        current_status = self.instance.status
        
        # Define allowed status transitions
        allowed_transitions = {
            'pending': ['confirmed', 'cancelled'],
            'confirmed': ['processing', 'cancelled'],
            'processing': ['ready_for_delivery', 'cancelled'],
            'ready_for_delivery': ['out_for_delivery'],
            'out_for_delivery': ['delivered', 'cancelled'],
            'delivered': [],  # Final state
            'cancelled': [],  # Final state
            'refunded': []    # Final state
        }
        
        if value not in allowed_transitions.get(current_status, []):
            raise serializers.ValidationError(
                f"Cannot change status from {current_status} to {value}"
            )
        
        return value


class OrderSummarySerializer(serializers.ModelSerializer):
    """
    Simplified serializer for order summaries.
    """
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    business_name = serializers.CharField(source='business.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    item_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Order
        fields = (
            'id', 'order_number', 'customer_name', 'business_name',
            'status', 'status_display', 'total_amount', 'item_count',
            'created_at', 'updated_at'
        )
    
    def get_item_count(self, obj):
        return obj.items.count()


class BusinessProductListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for listing business products.
    """
    
    class Meta:
        model = Product
        fields = (
            'id', 'name', 'description', 'price', 'category',
            'stock_quantity', 'status', 'is_in_stock'
        )
