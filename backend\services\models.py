from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid


class Service(models.Model):
    """
    Service model for service-based businesses (salons, consultants, etc.).
    """

    class ServiceStatus(models.TextChoices):
        ACTIVE = 'active', 'Active'
        INACTIVE = 'inactive', 'Inactive'
        COMING_SOON = 'coming_soon', 'Coming Soon'

    business = models.ForeignKey(
        'accounts.Business',
        on_delete=models.CASCADE,
        related_name='services',
        limit_choices_to={'business_type': 'service'}
    )

    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    # Service pricing
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Service price"
    )

    # Service duration
    duration_minutes = models.PositiveIntegerField(
        help_text="Service duration in minutes"
    )

    # Service details
    category = models.CharField(max_length=100, blank=True)
    image = models.ImageField(upload_to='services/', null=True, blank=True)

    # Service availability
    status = models.CharField(
        max_length=15,
        choices=ServiceStatus.choices,
        default=ServiceStatus.ACTIVE
    )

    # Booking settings
    requires_deposit = models.BooleanField(
        default=False,
        help_text="Whether this service requires a deposit"
    )
    deposit_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Deposit amount required"
    )

    # Buffer time after service
    buffer_time_minutes = models.PositiveIntegerField(
        default=0,
        help_text="Additional buffer time after service completion"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.business.name}"

    @property
    def total_duration_minutes(self):
        """Total time including buffer."""
        return self.duration_minutes + self.buffer_time_minutes

    @property
    def is_available(self):
        """Check if service is available for booking."""
        return self.status == self.ServiceStatus.ACTIVE

    class Meta:
        unique_together = ['business', 'name']
        ordering = ['name']


class ServiceAvailability(models.Model):
    """
    Service availability schedule for different days of the week.
    """

    class DayOfWeek(models.IntegerChoices):
        MONDAY = 1, 'Monday'
        TUESDAY = 2, 'Tuesday'
        WEDNESDAY = 3, 'Wednesday'
        THURSDAY = 4, 'Thursday'
        FRIDAY = 5, 'Friday'
        SATURDAY = 6, 'Saturday'
        SUNDAY = 7, 'Sunday'

    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='availability_schedule'
    )

    day_of_week = models.IntegerField(
        choices=DayOfWeek.choices,
        help_text="Day of the week (1=Monday, 7=Sunday)"
    )

    start_time = models.TimeField(help_text="Service start time")
    end_time = models.TimeField(help_text="Service end time")

    is_available = models.BooleanField(
        default=True,
        help_text="Whether the service is available on this day"
    )

    def __str__(self):
        return f"{self.service.name} - {self.get_day_of_week_display()}: {self.start_time}-{self.end_time}"

    class Meta:
        unique_together = ['service', 'day_of_week']
        ordering = ['day_of_week', 'start_time']


class Appointment(models.Model):
    """
    Appointment model for service bookings.
    """

    class AppointmentStatus(models.TextChoices):
        PENDING = 'pending', 'Pending Confirmation'
        CONFIRMED = 'confirmed', 'Confirmed'
        IN_PROGRESS = 'in_progress', 'In Progress'
        COMPLETED = 'completed', 'Completed'
        CANCELLED = 'cancelled', 'Cancelled'
        NO_SHOW = 'no_show', 'No Show'
        RESCHEDULED = 'rescheduled', 'Rescheduled'

    # Appointment identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    appointment_number = models.CharField(max_length=20, unique=True)

    # Relationships
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        limit_choices_to={'role': 'pulse'},
        related_name='appointments'
    )
    business = models.ForeignKey(
        'accounts.Business',
        on_delete=models.CASCADE,
        related_name='appointments',
        limit_choices_to={'business_type': 'service'}
    )
    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='appointments'
    )

    # Appointment details
    appointment_datetime = models.DateTimeField(
        help_text="Scheduled appointment date and time"
    )
    duration_minutes = models.PositiveIntegerField(
        help_text="Appointment duration in minutes"
    )

    status = models.CharField(
        max_length=20,
        choices=AppointmentStatus.choices,
        default=AppointmentStatus.PENDING
    )

    # Customer information
    customer_name = models.CharField(max_length=200, blank=True)
    customer_phone = models.CharField(max_length=17, blank=True)
    customer_email = models.EmailField(blank=True)

    # Appointment notes
    customer_notes = models.TextField(
        blank=True,
        help_text="Special requests or notes from customer"
    )
    business_notes = models.TextField(
        blank=True,
        help_text="Internal notes for the business"
    )

    # Pricing
    service_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Price at time of booking"
    )
    deposit_paid = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    confirmed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    cancelled_at = models.DateTimeField(null=True, blank=True)

    # WhatsApp integration
    whatsapp_message_id = models.CharField(max_length=100, blank=True)

    def __str__(self):
        return f"Appointment {self.appointment_number} - {self.customer.username} - {self.service.name}"

    def save(self, *args, **kwargs):
        if not self.appointment_number:
            # Generate appointment number
            import random
            import string
            self.appointment_number = 'APT' + ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))

        # Set service price and calculate totals
        if not self.service_price:
            self.service_price = self.service.price

        if not self.duration_minutes:
            self.duration_minutes = self.service.duration_minutes

        self.total_amount = self.service_price
        super().save(*args, **kwargs)

    @property
    def end_datetime(self):
        """Calculate appointment end time."""
        from datetime import timedelta
        return self.appointment_datetime + timedelta(minutes=self.duration_minutes)

    @property
    def is_past_due(self):
        """Check if appointment is past due."""
        from django.utils import timezone
        return self.appointment_datetime < timezone.now()

    @property
    def can_be_cancelled(self):
        """Check if appointment can be cancelled."""
        return self.status in [self.AppointmentStatus.PENDING, self.AppointmentStatus.CONFIRMED]

    @property
    def can_be_rescheduled(self):
        """Check if appointment can be rescheduled."""
        return self.status in [self.AppointmentStatus.PENDING, self.AppointmentStatus.CONFIRMED]

    class Meta:
        ordering = ['-appointment_datetime']
