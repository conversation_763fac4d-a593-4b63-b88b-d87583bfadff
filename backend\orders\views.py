"""
Views for the orders app.
"""

from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils import timezone

from .models import Product, Order, DailyMenu, DailyMenuItem
from .serializers import (
    ProductSerializer, ProductCreateSerializer, OrderSerializer,
    OrderCreateSerializer, OrderUpdateSerializer, OrderSummarySerializer,
    BusinessProductListSerializer, DailyMenuSerializer, DailyMenuItemSerializer,
    DailyMenuCreateSerializer
)
from accounts.permissions import (
    IsPod, IsPulse, IsCustomerOrBusinessOwner,
    IsBusinessOwnerOrReadOnly
)
from whatsapp.services import whatsapp_service


class ProductListCreateView(generics.ListCreateAPIView):
    """
    API view for listing and creating products.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ProductCreateSerializer
        return ProductSerializer

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return Product.objects.all()
        elif user.role == 'pod':
            # Business owners can see their own products
            business = getattr(user, 'business', None)
            if business:
                return Product.objects.filter(business=business)
        elif user.role in ['pulse', 'runner']:
            # Customers and runners can see active products
            return Product.objects.filter(status='active')

        return Product.objects.none()

    def get_permissions(self):
        if self.request.method == 'POST':
            return [permissions.IsAuthenticated(), IsPod()]
        return [permissions.IsAuthenticated()]


class ProductDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API view for product detail, update, and delete.
    """
    serializer_class = ProductSerializer
    permission_classes = [permissions.IsAuthenticated, IsBusinessOwnerOrReadOnly]

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return Product.objects.all()
        elif user.role == 'pod':
            business = getattr(user, 'business', None)
            if business:
                return Product.objects.filter(business=business)
        elif user.role in ['pulse', 'runner']:
            return Product.objects.filter(status='active')

        return Product.objects.none()


class BusinessProductListView(generics.ListAPIView):
    """
    API view for listing products of a specific business.
    """
    serializer_class = BusinessProductListSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        business_id = self.kwargs.get('business_id')
        return Product.objects.filter(
            business_id=business_id,
            status='active'
        )


class OrderListCreateView(generics.ListCreateAPIView):
    """
    API view for listing and creating orders.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return OrderCreateSerializer
        return OrderSummarySerializer

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return Order.objects.all()
        elif user.role == 'pod':
            # Business owners can see orders for their business
            business = getattr(user, 'business', None)
            if business:
                return Order.objects.filter(business=business)
        elif user.role == 'pulse':
            # Customers can see their own orders
            return Order.objects.filter(customer=user)
        elif user.role == 'runner':
            # Runners can see orders assigned to them for delivery
            return Order.objects.filter(delivery__runner=user)

        return Order.objects.none()

    def get_permissions(self):
        if self.request.method == 'POST':
            return [permissions.IsAuthenticated(), IsPulse()]
        return [permissions.IsAuthenticated()]

    def perform_create(self, serializer):
        order = serializer.save()

        # Send order confirmation message via WhatsApp
        self._send_order_confirmation(order)

    def _send_order_confirmation(self, order):
        """Send order confirmation message to customer."""
        try:
            message = f"""
🛍️ Order Confirmation

Order Number: {order.order_number}
Business: {order.business.name}
Total: R{order.total_amount}

Your order has been received and is being processed.
We'll keep you updated on the status.

Thank you for your order!
            """.strip()

            whatsapp_service.send_text_message(
                order.customer.phone_number,
                message,
                order.business
            )
        except Exception as e:
            # Log error but don't fail the order creation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to send order confirmation: {str(e)}")


class OrderDetailView(generics.RetrieveUpdateAPIView):
    """
    API view for order detail and update.
    """
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated, IsCustomerOrBusinessOwner]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return OrderUpdateSerializer
        return OrderSerializer

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return Order.objects.all()
        elif user.role == 'pod':
            business = getattr(user, 'business', None)
            if business:
                return Order.objects.filter(business=business)
        elif user.role == 'pulse':
            return Order.objects.filter(customer=user)
        elif user.role == 'runner':
            return Order.objects.filter(delivery__runner=user)

        return Order.objects.none()

    def perform_update(self, serializer):
        order = serializer.save()

        # Send status update notification
        if 'status' in serializer.validated_data:
            self._send_status_update(order)

    def _send_status_update(self, order):
        """Send order status update to customer."""
        try:
            status_messages = {
                'confirmed': f"✅ Your order {order.order_number} has been confirmed and is being prepared.",
                'processing': f"🔄 Your order {order.order_number} is being processed.",
                'ready_for_delivery': f"📦 Your order {order.order_number} is ready for delivery.",
                'out_for_delivery': f"🚚 Your order {order.order_number} is out for delivery.",
                'delivered': f"✅ Your order {order.order_number} has been delivered. Thank you!",
                'cancelled': f"❌ Your order {order.order_number} has been cancelled."
            }

            message = status_messages.get(order.status)
            if message:
                whatsapp_service.send_text_message(
                    order.customer.phone_number,
                    message,
                    order.business
                )
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to send status update: {str(e)}")


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsPod])
def confirm_order(request, order_id):
    """
    API endpoint for confirming an order.
    """
    try:
        business = getattr(request.user, 'business', None)
        order = Order.objects.get(id=order_id, business=business)

        if order.status != 'pending':
            return Response({
                'error': 'Order can only be confirmed from pending status'
            }, status=status.HTTP_400_BAD_REQUEST)

        order.status = 'confirmed'
        order.confirmed_at = timezone.now()
        order.save()

        # Send confirmation message
        message = f"""
✅ Order Confirmed!

Order Number: {order.order_number}
Total: R{order.total_amount}

Your order has been confirmed by {order.business.name}.
We'll start preparing it right away!
        """.strip()

        whatsapp_service.send_text_message(
            order.customer.phone_number,
            message,
            order.business
        )

        return Response({
            'message': 'Order confirmed successfully',
            'order': OrderSerializer(order).data
        }, status=status.HTTP_200_OK)

    except Order.DoesNotExist:
        return Response({
            'error': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


class DailyMenuListCreateView(generics.ListCreateAPIView):
    """
    API view for listing and creating daily menus.
    """
    permission_classes = [permissions.IsAuthenticated, IsPod]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DailyMenuCreateSerializer
        return DailyMenuSerializer

    def get_queryset(self):
        business = getattr(self.request.user, 'business', None)
        if business and business.is_food_business:
            return DailyMenu.objects.filter(business=business)
        return DailyMenu.objects.none()

    def perform_create(self, serializer):
        business = getattr(self.request.user, 'business', None)
        if not business or not business.is_food_business:
            raise permissions.PermissionDenied("Only food businesses can create daily menus.")
        serializer.save()


class DailyMenuDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API view for daily menu detail, update, and delete.
    """
    serializer_class = DailyMenuSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return DailyMenu.objects.all()
        elif user.role == 'pod':
            business = getattr(user, 'business', None)
            if business:
                return DailyMenu.objects.filter(business=business)
        elif user.role == 'pulse':
            # Customers can view active menus
            return DailyMenu.objects.filter(is_active=True)

        return DailyMenu.objects.none()


class DailyMenuItemListCreateView(generics.ListCreateAPIView):
    """
    API view for managing daily menu items.
    """
    serializer_class = DailyMenuItemSerializer
    permission_classes = [permissions.IsAuthenticated, IsPod]

    def get_queryset(self):
        menu_id = self.kwargs.get('menu_id')
        business = getattr(self.request.user, 'business', None)

        if business:
            return DailyMenuItem.objects.filter(
                daily_menu_id=menu_id,
                daily_menu__business=business
            )
        return DailyMenuItem.objects.none()


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def todays_menu(request, business_id=None):
    """
    API endpoint for getting today's menu for a business.
    """
    from django.utils import timezone
    today = timezone.now().date()

    try:
        if business_id:
            # Get specific business menu
            from accounts.models import Business
            business = Business.objects.get(id=business_id, business_type='food_restaurant')
        else:
            # Get current user's business menu
            business = getattr(request.user, 'business', None)
            if not business or not business.is_food_business:
                return Response({
                    'error': 'No food business found'
                }, status=status.HTTP_404_NOT_FOUND)

        try:
            daily_menu = DailyMenu.objects.get(
                business=business,
                date=today,
                is_active=True
            )
            serializer = DailyMenuSerializer(daily_menu)
            return Response(serializer.data)

        except DailyMenu.DoesNotExist:
            return Response({
                'business_name': business.name,
                'date': today.isoformat(),
                'message': 'No menu available for today',
                'menu_items': []
            })

    except Business.DoesNotExist:
        return Response({
            'error': 'Business not found'
        }, status=status.HTTP_404_NOT_FOUND)
